require('dotenv').config();
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const cookieParser = require('cookie-parser');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3000;
const SESSION_SECRET = process.env.SESSION_SECRET || crypto.randomBytes(32).toString('hex');
const IGDB_CLIENT_ID = process.env.IGDB_CLIENT_ID;
const IGDB_CLIENT_SECRET = process.env.IGDB_CLIENT_SECRET;
const TWITCH_CLIENT_ID = process.env.TWITCH_CLIENT_ID;
const TWITCH_CLIENT_SECRET = process.env.TWITCH_CLIENT_SECRET;
const YOUTUBE_API_KEY = process.env.YOUTUBE_API_KEY;
const CONFIG_SERVER_URL = process.env.CONFIG_SERVER_URL || 'http://localhost:3001';
const ADMINS = process.env.ADMINS ? process.env.ADMINS.split(',') : [];

// Import config client
const ConfigClient = require('./config-api/config-client');
const configClient = new ConfigClient(CONFIG_SERVER_URL);
const DATA_FILE = path.join(__dirname, 'data.json');
const COOKIE_NAME = 'xennygames_session';
const COOKIE_OPTIONS = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict',
  maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
};

// Initialize data structure
let gameData = {
  games: {},
  votes: {}
};

// Load existing data
if (fs.existsSync(DATA_FILE)) {
  try {
    gameData = JSON.parse(fs.readFileSync(DATA_FILE));
  } catch (e) {
    console.error('Error loading data file:', e);
  }
}

// Save data to file
function saveData() {
  try {
    fs.writeFileSync(DATA_FILE, JSON.stringify(gameData, null, 2));
    return true;
  } catch (e) {
    console.error('Error saving data:', e);
    return false;
  }
}

// Twitch token validation
async function validateTwitchToken(token) {
  try {
    console.log('[DEBUG] Fetching user info from Twitch Helix API');
    
    const response = await axios.get('https://api.twitch.tv/helix/users', {
      headers: { 
        'Authorization': `Bearer ${token}`,
        'Client-ID': TWITCH_CLIENT_ID
      }
    });

    console.log('[DEBUG] Twitch Helix response:', JSON.stringify(response.data, null, 2));
    
    if (!response.data.data || response.data.data.length === 0) {
      console.error('[ERROR] No user data in Twitch response');
      return null;
    }

    const userData = response.data.data[0];
    return {
      preferred_username: userData.login,
      display_name: userData.display_name,
      picture: userData.profile_image_url, // This matches Python's profile_image_url
      ...userData
    };
  } catch (error) {
    console.error('[ERROR] Twitch token validation failed:', {
      message: error.message,
      response: error.response?.data,
      stack: error.stack
    });
    return null;
  }
}

// IGDB API helper
async function getIgdbAccessToken() {
  try {
    const response = await axios.post(
      `https://id.twitch.tv/oauth2/token?client_id=${IGDB_CLIENT_ID}&client_secret=${IGDB_CLIENT_SECRET}&grant_type=client_credentials`
    );
    return response.data.access_token;
  } catch (error) {
    console.error('IGDB token error:', error.response?.data || error.message);
    return null;
  }
}

// Search IGDB
async function searchIGDB(query) {
  try {
    const token = await getIgdbAccessToken();
    if (!token) return [];
    
    const response = await axios.post(
      'https://api.igdb.com/v4/games',
      `fields name,cover.url; search "${query}"; limit 20; where version_parent = null & category = 0;`,
      {
        headers: {
          'Client-ID': IGDB_CLIENT_ID,
          'Authorization': `Bearer ${token}`
        }
      }
    );
    
    return response.data.map(game => ({
      id: game.id,
      name: game.name,
      cover_url: game.cover ? `https:${game.cover.url.replace('t_thumb', 't_cover_big')}` : null
    }));
  } catch (error) {
    console.error('IGDB search error:', error.response?.data || error.message);
    return [];
  }
}

// Create JWT token
function createToken(user) {
  return jwt.sign({
    username: user.preferred_username,
    display_name: user.display_name,
    avatar: user.picture,
    isAdmin: ADMINS.includes(user.preferred_username)
  }, SESSION_SECRET, { expiresIn: '7d' });
}
// Verify JWT token
function verifyToken(token) {
  try {
    return jwt.verify(token, SESSION_SECRET);
  } catch (error) {
    console.error('Token verification failed:', error.message);
    return null;
  }
}

// Middleware
app.use(express.json());
app.use(cookieParser());
app.use(express.static('public'));

app.post('/api/login', async (req, res) => {
  try {
    console.log('[DEBUG] /api/login request received');
    
    const { token } = req.body;
    if (!token) {
      console.error('[ERROR] No token provided');
      return res.status(400).json({ error: 'Token is required' });
    }

    console.log('[DEBUG] Validating Twitch token...');
    const user = await validateTwitchToken(token);
    
    if (!user) {
      console.error('[ERROR] Token validation failed');
      return res.status(401).json({ error: 'Invalid token' });
    }

    console.log('[DEBUG] User data:', {
      username: user.login,
      display_name: user.display_name,
      avatar: user.profile_image_url,
      adminCheck: ADMINS.includes(user.login)
    });

    const sessionToken = createToken({
      preferred_username: user.login,
      picture: user.profile_image_url,
      display_name: user.display_name
    });
    
    res.cookie(COOKIE_NAME, sessionToken, COOKIE_OPTIONS).json({
      username: user.login,
      display_name: user.display_name,
      avatar: user.profile_image_url,
      isAdmin: ADMINS.includes(user.login)
    });

    console.log('[DEBUG] Login successful for:', user.login);
  } catch (error) {
    console.error('[ERROR] Login failed:', {
      message: error.message,
      stack: error.stack,
      requestBody: req.body
    });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/logout', (req, res) => {
  res.clearCookie(COOKIE_NAME).json({ message: 'Logged out' });
});

app.get('/api/session', (req, res) => {
  const token = req.cookies[COOKIE_NAME];
  if (!token) return res.status(401).json({ error: 'No session' });
  
  const user = verifyToken(token);
  if (!user) return res.status(401).json({ error: 'Invalid session' });
  
  res.json({
    username: user.username,
    display_name: user.display_name,
    avatar: user.avatar,
    isAdmin: user.isAdmin
  });
});

app.post('/api/games', (req, res) => {
  try {
    const token = req.cookies[COOKIE_NAME];
    const user = verifyToken(token);
    if (!user || !user.isAdmin) return res.status(403).json({ error: 'Forbidden' });
    
    const { game } = req.body;
    const gameName = game.name.trim();
    if (!gameName) return res.status(400).json({ error: 'Game name required' });
    
    if (gameData.games[gameName]) {
      return res.status(409).json({ error: 'Game already exists' });
    }
    
    gameData.games[gameName] = {
      ...game,
      createdAt: new Date().toISOString(),
      overlay: 'Auf der Spieleliste',
      requested_by: user.username
    };
    
    if (!gameData.votes[gameName]) {
      gameData.votes[gameName] = { score: 0, votes: {} };
    }
    
    saveData();
    io.emit('update', gameData);
    res.status(201).json({ message: 'Game added' });
  } catch (error) {
    console.error('Add game error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/vote', (req, res) => {
    try {
        const token = req.cookies[COOKIE_NAME];
        const user = verifyToken(token);
        if (!user) return res.status(401).json({ error: 'Unauthorized' });
        
        const { gameName, voteType } = req.body;
        const vote = gameData.votes[gameName];
        
        if (!gameData.games[gameName]) return res.status(404).json({ error: 'Game not found' });
        
        const currentVote = vote.votes[user.username];
        let scoreChange = 0;
        
        // Clear previous vote
        if (currentVote === 'up') scoreChange -= 1;
        if (currentVote === 'down') scoreChange += 1;
        
        // Apply new vote (or remove if voteType is null)
        if (voteType === 'up') {
            vote.votes[user.username] = 'up';
            scoreChange += 1;
        } else if (voteType === 'down') {
            vote.votes[user.username] = 'down';
            scoreChange -= 1;
        } else {
            delete vote.votes[user.username];
        }
        
        vote.score += scoreChange;
        saveData();
        io.emit('update', gameData);
        res.json({ score: vote.score });
    } catch (error) {
        console.error('Vote error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

app.post('/api/game/update', (req, res) => {
  try {
    const token = req.cookies[COOKIE_NAME];
    const user = verifyToken(token);
    if (!user || !user.isAdmin) return res.status(403).json({ error: 'Forbidden' });

    const { gameName, updates } = req.body;
    if (!gameData.games[gameName]) return res.status(404).json({ error: 'Game not found' });

    // If the overlay (category) is being changed, add a timestamp for when it was moved
    if (updates.overlay && updates.overlay !== gameData.games[gameName].overlay) {
      updates.movedToCurrentCategoryAt = new Date().toISOString();
    }

    gameData.games[gameName] = {
      ...gameData.games[gameName],
      ...updates
    };

    saveData();
    io.emit('update', gameData);
    res.json({ message: 'Game updated' });
  } catch (error) {
    console.error('Update game error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Helper function to validate YouTube playlist URL
function validateYouTubePlaylistUrl(url) {
  if (!url) return null;

  // Match various YouTube playlist URL formats
  const playlistRegex = /(?:youtube\.com\/playlist\?list=|youtube\.com\/watch\?.*list=)([a-zA-Z0-9_-]+)/;
  const match = url.match(playlistRegex);

  if (match) {
    return `https://www.youtube.com/playlist?list=${match[1]}`;
  }

  return null;
}

// Helper function to extract playlist ID from URL
function extractPlaylistId(url) {
  if (!url) return null;

  const playlistRegex = /(?:youtube\.com\/playlist\?list=|youtube\.com\/watch\?.*list=)([a-zA-Z0-9_-]+)/;
  const match = url.match(playlistRegex);

  return match ? match[1] : null;
}

// YouTube API helper to get playlist info
async function getYouTubePlaylistInfo(playlistId) {
  if (!YOUTUBE_API_KEY) {
    console.warn('YouTube API key not configured');
    return null;
  }

  try {
    const response = await axios.get('https://www.googleapis.com/youtube/v3/playlists', {
      params: {
        part: 'snippet,contentDetails',
        id: playlistId,
        key: YOUTUBE_API_KEY
      }
    });

    if (response.data.items && response.data.items.length > 0) {
      const playlist = response.data.items[0];
      return {
        title: playlist.snippet.title,
        itemCount: playlist.contentDetails.itemCount,
        description: playlist.snippet.description,
        thumbnails: playlist.snippet.thumbnails
      };
    }

    return null;
  } catch (error) {
    console.error('YouTube API error:', error.response?.data || error.message);
    return null;
  }
}

// New endpoint specifically for updating YouTube playlist
app.post('/api/game/playlist', async (req, res) => {
  try {
    const token = req.cookies[COOKIE_NAME];
    const user = verifyToken(token);
    if (!user || !user.isAdmin) return res.status(403).json({ error: 'Forbidden' });

    const { gameName, playlistUrl } = req.body;
    if (!gameData.games[gameName]) return res.status(404).json({ error: 'Game not found' });

    // Validate and normalize the YouTube playlist URL
    const validatedUrl = validateYouTubePlaylistUrl(playlistUrl);
    if (playlistUrl && !validatedUrl) {
      return res.status(400).json({ error: 'Invalid YouTube playlist URL' });
    }

    let playlistInfo = null;
    if (validatedUrl) {
      const playlistId = extractPlaylistId(validatedUrl);
      if (playlistId) {
        playlistInfo = await getYouTubePlaylistInfo(playlistId);
      }
    }

    gameData.games[gameName] = {
      ...gameData.games[gameName],
      youtube_playlist: validatedUrl || null,
      youtube_playlist_info: playlistInfo
    };

    saveData();
    io.emit('update', gameData);
    res.json({
      message: 'Playlist updated',
      playlist: validatedUrl,
      playlistInfo: playlistInfo
    });
  } catch (error) {
    console.error('Update playlist error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/game/remove', (req, res) => {
  try {
    const token = req.cookies[COOKIE_NAME];
    const user = verifyToken(token);
    if (!user || !user.isAdmin) return res.status(403).json({ error: 'Forbidden' });
    
    const { gameName } = req.body;
    if (!gameData.games[gameName]) return res.status(404).json({ error: 'Game not found' });
    
    delete gameData.games[gameName];
    delete gameData.votes[gameName];
    
    saveData();
    io.emit('update', gameData);
    res.json({ message: 'Game removed' });
  } catch (error) {
    console.error('Remove game error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Endpoint to refresh playlist information for a game
app.post('/api/game/playlist/refresh', async (req, res) => {
  try {
    const token = req.cookies[COOKIE_NAME];
    const user = verifyToken(token);
    if (!user || !user.isAdmin) return res.status(403).json({ error: 'Forbidden' });

    const { gameName } = req.body;
    const game = gameData.games[gameName];

    if (!game) return res.status(404).json({ error: 'Game not found' });
    if (!game.youtube_playlist) return res.status(400).json({ error: 'No playlist URL found' });

    const playlistId = extractPlaylistId(game.youtube_playlist);
    if (!playlistId) return res.status(400).json({ error: 'Invalid playlist URL' });

    const playlistInfo = await getYouTubePlaylistInfo(playlistId);

    gameData.games[gameName] = {
      ...game,
      youtube_playlist_info: playlistInfo
    };

    saveData();
    io.emit('update', gameData);
    res.json({
      message: 'Playlist info refreshed',
      playlistInfo: playlistInfo
    });
  } catch (error) {
    console.error('Refresh playlist error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Endpoint to refresh all playlist information
app.post('/api/playlists/refresh-all', async (req, res) => {
  try {
    const token = req.cookies[COOKIE_NAME];
    const user = verifyToken(token);
    if (!user || !user.isAdmin) return res.status(403).json({ error: 'Forbidden' });

    const gamesWithPlaylists = Object.entries(gameData.games)
      .filter(([name, game]) => game.youtube_playlist)
      .map(([name, game]) => ({ name, url: game.youtube_playlist }));

    let updatedCount = 0;
    let errorCount = 0;

    for (const { name, url } of gamesWithPlaylists) {
      try {
        const playlistId = extractPlaylistId(url);
        if (playlistId) {
          const playlistInfo = await getYouTubePlaylistInfo(playlistId);
          if (playlistInfo) {
            gameData.games[name].youtube_playlist_info = playlistInfo;
            updatedCount++;
          }
        }
        // Add small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error(`Failed to update playlist for ${name}:`, error);
        errorCount++;
      }
    }

    saveData();
    io.emit('update', gameData);

    res.json({
      message: 'Playlist refresh completed',
      updated: updatedCount,
      errors: errorCount,
      total: gamesWithPlaylists.length
    });
  } catch (error) {
    console.error('Bulk playlist refresh error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Config server integration endpoints
app.get('/api/config/playlists', async (req, res) => {
  try {
    const token = req.cookies[COOKIE_NAME];
    const user = verifyToken(token);
    if (!user || !user.isAdmin) return res.status(403).json({ error: 'Forbidden' });

    const playlistData = await configClient.getAllPlaylists();
    res.json(playlistData);
  } catch (error) {
    console.error('Error fetching config playlists:', error);
    res.status(500).json({ error: 'Failed to fetch playlists from config server' });
  }
});

app.post('/api/config/sync', async (req, res) => {
  try {
    const token = req.cookies[COOKIE_NAME];
    const user = verifyToken(token);
    if (!user || !user.isAdmin) return res.status(403).json({ error: 'Forbidden' });

    const syncedData = await configClient.syncPlaylistsWithGames(gameData);

    // Update the game data with synced playlists
    gameData = syncedData;

    saveData();
    io.emit('update', gameData);

    res.json({
      message: 'Playlists synced successfully',
      stats: syncedData.syncStats
    });
  } catch (error) {
    console.error('Error syncing with config server:', error);
    res.status(500).json({ error: 'Failed to sync with config server' });
  }
});

app.get('/api/config/health', async (req, res) => {
  try {
    const token = req.cookies[COOKIE_NAME];
    const user = verifyToken(token);
    if (!user || !user.isAdmin) return res.status(403).json({ error: 'Forbidden' });

    const isHealthy = await configClient.isHealthy();
    const cacheStats = configClient.getCacheStats();

    res.json({
      configServer: {
        url: CONFIG_SERVER_URL,
        healthy: isHealthy
      },
      cache: cacheStats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error checking config server health:', error);
    res.status(500).json({ error: 'Failed to check config server health' });
  }
});

app.post('/api/igdb/search', async (req, res) => {
  try {
    const token = req.cookies[COOKIE_NAME];
    const user = verifyToken(token);
    if (!user || !user.isAdmin) return res.status(403).json({ error: 'Forbidden' });
    
    const { query } = req.body;
    const results = await searchIGDB(query);
    res.json(results);
  } catch (error) {
    console.error('IGDB search error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Socket.IO for live updates
io.on('connection', (socket) => {
  socket.emit('init', gameData);
  
  socket.on('disconnect', () => {
    console.log('Client disconnected');
  });
});

server.listen(PORT, async () => {
  console.log(`Server running on http://localhost:${PORT}`);
  console.log(`Admins: ${ADMINS.join(', ') || 'None configured'}`);
  console.log(`Config server URL: ${CONFIG_SERVER_URL}`);

  // Try to sync with config server on startup
  try {
    const isHealthy = await configClient.isHealthy();
    if (isHealthy) {
      console.log('Config server is healthy, attempting initial sync...');
      const syncedData = await configClient.syncPlaylistsWithGames(gameData);

      if (syncedData.syncStats.gamesUpdated > 0) {
        gameData = syncedData;
        saveData();
        console.log(`Initial sync completed: ${syncedData.syncStats.gamesUpdated} games updated with playlists`);
      } else {
        console.log('Initial sync completed: No games needed playlist updates');
      }
    } else {
      console.warn('Config server is not healthy, skipping initial sync');
    }
  } catch (error) {
    console.warn('Failed to sync with config server on startup:', error.message);
  }
});