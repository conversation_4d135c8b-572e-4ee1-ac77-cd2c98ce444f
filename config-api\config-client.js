const axios = require('axios');

class ConfigClient {
    constructor(configServerUrl = 'http://localhost:3001') {
        this.baseUrl = configServerUrl;
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes cache
    }

    /**
     * Get all playlists from config server
     * @returns {Promise<Object>} Object with playlist data
     */
    async getAllPlaylists() {
        const cacheKey = 'all_playlists';
        
        // Check cache first
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }

        try {
            const response = await axios.get(`${this.baseUrl}/api/playlists`, {
                timeout: 5000
            });
            
            const data = response.data;
            
            // Cache the result
            this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
            });
            
            return data;
        } catch (error) {
            console.error('Error fetching all playlists:', error.message);
            
            // Return cached data if available, even if expired
            if (this.cache.has(cacheKey)) {
                console.log('Returning cached data due to error');
                return this.cache.get(cacheKey).data;
            }
            
            throw new Error(`Failed to fetch playlists: ${error.message}`);
        }
    }

    /**
     * Get playlist for specific game
     * @param {string} gameName - Name of the game
     * @returns {Promise<Object>} Playlist data for the game
     */
    async getPlaylistForGame(gameName) {
        const cacheKey = `playlist_${gameName}`;
        
        // Check cache first
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }

        try {
            const response = await axios.get(`${this.baseUrl}/api/playlists/${encodeURIComponent(gameName)}`, {
                timeout: 5000
            });
            
            const data = response.data;
            
            // Cache the result
            this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
            });
            
            return data;
        } catch (error) {
            if (error.response && error.response.status === 404) {
                return null; // Game not found
            }
            
            console.error(`Error fetching playlist for ${gameName}:`, error.message);
            
            // Return cached data if available
            if (this.cache.has(cacheKey)) {
                console.log('Returning cached data due to error');
                return this.cache.get(cacheKey).data;
            }
            
            throw new Error(`Failed to fetch playlist for ${gameName}: ${error.message}`);
        }
    }

    /**
     * Get simple mapping of game names to playlist IDs
     * @returns {Promise<Object>} Simple mapping object
     */
    async getPlaylistMapping() {
        const cacheKey = 'playlist_mapping';
        
        // Check cache first
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }

        try {
            const response = await axios.get(`${this.baseUrl}/api/playlist-mapping`, {
                timeout: 5000
            });
            
            const data = response.data;
            
            // Cache the result
            this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
            });
            
            return data;
        } catch (error) {
            console.error('Error fetching playlist mapping:', error.message);
            
            // Return cached data if available
            if (this.cache.has(cacheKey)) {
                console.log('Returning cached data due to error');
                return this.cache.get(cacheKey).data;
            }
            
            throw new Error(`Failed to fetch playlist mapping: ${error.message}`);
        }
    }

    /**
     * Check if config server is healthy
     * @returns {Promise<boolean>} True if server is healthy
     */
    async isHealthy() {
        try {
            const response = await axios.get(`${this.baseUrl}/api/health`, {
                timeout: 3000
            });
            return response.data.status === 'ok';
        } catch (error) {
            console.error('Config server health check failed:', error.message);
            return false;
        }
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
    }

    /**
     * Get cache stats
     * @returns {Object} Cache statistics
     */
    getCacheStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys()),
            timeout: this.cacheTimeout
        };
    }

    /**
     * Sync playlists with XennyGames database
     * @param {Object} gameData - Current game data from XennyGames
     * @returns {Promise<Object>} Updated game data with playlists
     */
    async syncPlaylistsWithGames(gameData) {
        try {
            const playlistData = await this.getAllPlaylists();
            const playlists = playlistData.playlists || {};
            
            let updatedCount = 0;
            const updatedGames = { ...gameData.games };
            
            // Update games with playlist information
            for (const [gameName, gameInfo] of Object.entries(updatedGames)) {
                if (playlists[gameName]) {
                    const playlist = playlists[gameName];
                    
                    // Only update if playlist URL is different or missing
                    if (!gameInfo.youtube_playlist || gameInfo.youtube_playlist !== playlist.url) {
                        updatedGames[gameName] = {
                            ...gameInfo,
                            youtube_playlist: playlist.url,
                            youtube_playlist_source: 'config_server',
                            youtube_playlist_updated: new Date().toISOString()
                        };
                        updatedCount++;
                    }
                }
            }
            
            return {
                games: updatedGames,
                votes: gameData.votes,
                syncStats: {
                    totalGames: Object.keys(updatedGames).length,
                    playlistsAvailable: Object.keys(playlists).length,
                    gamesUpdated: updatedCount,
                    lastSync: new Date().toISOString()
                }
            };
        } catch (error) {
            console.error('Error syncing playlists with games:', error);
            throw error;
        }
    }
}

module.exports = ConfigClient;
